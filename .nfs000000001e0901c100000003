2025-06-08 02:49:55,539 - __main__ - INFO - 🚀 Starting MASK Benchmark Truthfulness Steering Pipeline
2025-06-08 02:49:55,539 - __main__ - INFO - Model: meta-llama/Llama-3.1-8B-Instruct
2025-06-08 02:49:55,539 - __main__ - INFO - SAE: /data_x/junkim100/projects/scheming_sae/itas/models/sae/llama_3_1_8b_layer16_gated_sae.pt
2025-06-08 02:49:55,540 - __main__ - INFO - Target layer: 16
2025-06-08 02:49:55,540 - __main__ - INFO - Steering alpha: 1.5
2025-06-08 02:49:55,541 - __main__ - INFO - Max train scenarios: ALL
2025-06-08 02:49:55,542 - __main__ - INFO - Max test scenarios: ALL
2025-06-08 02:49:55,543 - __main__ - INFO - Using device: cuda
2025-06-08 02:49:55,545 - __main__ - INFO - 📥 Loading model and tokenizer...
2025-06-08 02:49:55,545 - itas.core.model_loader - INFO - Loading model: meta-llama/Llama-3.1-8B-Instruct
2025-06-08 02:50:03,000 - itas.core.model_loader - INFO - Enabled gradient checkpointing
2025-06-08 02:50:03,002 - itas.core.model_loader - INFO - Successfully loaded model: meta-llama/Llama-3.1-8B-Instruct
2025-06-08 02:50:03,002 - itas.core.model_loader - INFO - Model architecture: ['LlamaForCausalLM']
2025-06-08 02:50:03,004 - itas.core.model_loader - INFO - Model parameters: 8,030,261,248
2025-06-08 02:50:03,005 - __main__ - INFO - 📥 Loading SAE...
2025-06-08 02:50:08,882 - __main__ - INFO - 📊 Loading training data...
2025-06-08 02:50:08,883 - __main__ - INFO - Loading MASK data from ./data/mask_benchmark/csv_data/
2025-06-08 02:50:08,884 - __main__ - INFO - Found 6 CSV files: ['statistics.csv', 'known_facts.csv', 'continuations.csv', 'provided_facts.csv', 'doubling_down_known_facts.csv', 'disinformation.csv']
2025-06-08 02:50:08,885 - __main__ - INFO - Processing statistics.csv
2025-06-08 02:50:08,903 - __main__ - INFO -   Added 0 scenarios from statistics.csv
2025-06-08 02:50:08,903 - __main__ - INFO - Processing known_facts.csv
2025-06-08 02:50:08,934 - __main__ - INFO -   Added 209 scenarios from known_facts.csv
2025-06-08 02:50:08,934 - __main__ - INFO - Processing continuations.csv
2025-06-08 02:50:08,953 - __main__ - INFO -   Added 176 scenarios from continuations.csv
2025-06-08 02:50:08,953 - __main__ - INFO - Processing provided_facts.csv
2025-06-08 02:50:08,978 - __main__ - INFO -   Added 274 scenarios from provided_facts.csv
2025-06-08 02:50:08,978 - __main__ - INFO - Processing doubling_down_known_facts.csv
2025-06-08 02:50:08,993 - __main__ - INFO -   Added 120 scenarios from doubling_down_known_facts.csv
2025-06-08 02:50:08,993 - __main__ - INFO - Processing disinformation.csv
2025-06-08 02:50:09,006 - __main__ - INFO -   Added 125 scenarios from disinformation.csv
2025-06-08 02:50:09,006 - __main__ - INFO - Total scenarios loaded: 904
2025-06-08 02:50:09,007 - __main__ - INFO - 🧠 Extracting training activations...
2025-06-08 02:50:09,007 - __main__ - INFO - Extracting scheming activations from 904 scenarios
2025-06-08 02:50:09,007 - __main__ - INFO -   Processing scenario 1/904
2025-06-08 02:50:12,966 - __main__ - INFO -   Processing scenario 51/904
2025-06-08 02:50:16,037 - __main__ - INFO -   Processing scenario 101/904
2025-06-08 02:50:18,668 - __main__ - INFO -   Processing scenario 151/904
2025-06-08 02:50:21,857 - __main__ - INFO -   Processing scenario 201/904
2025-06-08 02:50:25,154 - __main__ - INFO -   Processing scenario 251/904
2025-06-08 02:50:28,497 - __main__ - INFO -   Processing scenario 301/904
2025-06-08 02:50:32,085 - __main__ - INFO -   Processing scenario 351/904
2025-06-08 02:50:35,380 - __main__ - INFO -   Processing scenario 401/904
2025-06-08 02:50:38,724 - __main__ - INFO -   Processing scenario 451/904
2025-06-08 02:50:41,661 - __main__ - INFO -   Processing scenario 501/904
2025-06-08 02:50:44,493 - __main__ - INFO -   Processing scenario 551/904
2025-06-08 02:50:48,007 - __main__ - INFO -   Processing scenario 601/904
2025-06-08 02:50:52,174 - __main__ - INFO -   Processing scenario 651/904
2025-06-08 02:50:55,176 - __main__ - INFO -   Processing scenario 701/904
2025-06-08 02:50:57,574 - __main__ - INFO -   Processing scenario 751/904
2025-06-08 02:51:00,000 - __main__ - INFO -   Processing scenario 801/904
2025-06-08 02:51:02,437 - __main__ - INFO -   Processing scenario 851/904
2025-06-08 02:51:04,941 - __main__ - INFO -   Processing scenario 901/904
2025-06-08 02:51:05,135 - __main__ - INFO - Extracted 904 scheming activations
2025-06-08 02:51:05,136 - __main__ - INFO - Extracting truthful activations from 904 scenarios
2025-06-08 02:51:05,136 - __main__ - INFO -   Processing scenario 1/904
2025-06-08 02:51:07,580 - __main__ - INFO -   Processing scenario 51/904
2025-06-08 02:51:10,140 - __main__ - INFO -   Processing scenario 101/904
2025-06-08 02:51:12,738 - __main__ - INFO -   Processing scenario 151/904
2025-06-08 02:51:15,886 - __main__ - INFO -   Processing scenario 201/904
2025-06-08 02:51:19,130 - __main__ - INFO -   Processing scenario 251/904
2025-06-08 02:51:22,471 - __main__ - INFO -   Processing scenario 301/904
2025-06-08 02:51:26,060 - __main__ - INFO -   Processing scenario 351/904
2025-06-08 02:51:29,331 - __main__ - INFO -   Processing scenario 401/904
2025-06-08 02:51:32,694 - __main__ - INFO -   Processing scenario 451/904
2025-06-08 02:51:35,809 - __main__ - INFO -   Processing scenario 501/904
2025-06-08 02:51:38,706 - __main__ - INFO -   Processing scenario 551/904
2025-06-08 02:51:42,285 - __main__ - INFO -   Processing scenario 601/904
2025-06-08 02:51:46,537 - __main__ - INFO -   Processing scenario 651/904
2025-06-08 02:51:49,561 - __main__ - INFO -   Processing scenario 701/904
2025-06-08 02:51:51,961 - __main__ - INFO -   Processing scenario 751/904
2025-06-08 02:51:54,357 - __main__ - INFO -   Processing scenario 801/904
2025-06-08 02:51:56,866 - __main__ - INFO -   Processing scenario 851/904
2025-06-08 02:51:59,416 - __main__ - INFO -   Processing scenario 901/904
2025-06-08 02:51:59,612 - __main__ - INFO - Extracted 904 truthful activations
2025-06-08 02:51:59,613 - __main__ - INFO - 🎯 Creating steering intervention...
2025-06-08 02:51:59,613 - itas.core.steering - INFO - Initialized KnowledgeSelectionSteering with SAE on cuda
2025-06-08 02:51:59,613 - itas.core.steering - INFO - Starting mutual information calculation for scheming vs truthfulness steering
2025-06-08 02:51:59,616 - itas.core.steering - INFO - Extracting SAE activations
