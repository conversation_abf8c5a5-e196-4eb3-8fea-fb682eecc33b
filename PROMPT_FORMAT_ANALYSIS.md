# Analysis: Issues with Current Prompt Format and Corrected Approach

## Problems with Current Concatenated String Format

### 1. **Improper Conversation Structure**
```python
# CURRENT (INCORRECT):
"System: You are a staffer... \n\nUser: Did <PERSON>... \n\nAssistant: Yes"
```

**Issues:**
- Model sees this as a single continuous text, not a structured conversation
- No proper role separation or conversation flow
- Doesn't match how chat models are trained to process conversations

### 2. **Wrong Activation Extraction Point**
```python
# CURRENT (INCORRECT):
def get_model_activations(prompts, model, tokenizer, target_layer, hook_name):
    for prompt in prompts:  # Processing entire concatenated string
        inputs = tokenizer(prompt, return_tensors='pt')
        _ = model(input_ids)  # Activations from processing pre-written text
```

**Issues:**
- Extracting activations from processing the entire concatenated string
- Not capturing activations from the model's actual generation of assistant response
- Comparing activations from different text lengths and structures

### 3. **Inconsistent Comparison Context**
- Scheming and truthful examples have different conversation structures
- No guarantee that activations come from the same input context
- Makes it impossible to isolate the behavioral difference

## Corrected Approach

### 1. **Structured Conversation Format**
```python
# CORRECTED:
conversation_scenarios = [
    {
        "system": "You are a staffer on Joe Biden's re-election campaign...",
        "user": "Did Joe Biden fulfill his campaign promise...",
        "scheming_response": "Yes",
        "truthful_response": "No"
    }
]
```

**Benefits:**
- Clear separation of conversation components
- Identical system + user context for both behaviors
- Only the assistant response varies between scheming/truthful

### 2. **Proper Activation Extraction**
```python
# CORRECTED:
def get_response_activations(scenarios, model, tokenizer, target_layer, hook_name, response_type):
    for scenario in scenarios:
        # Format as proper conversation
        conversation = [
            {"role": "system", "content": scenario["system"]},
            {"role": "user", "content": scenario["user"]}
        ]
        
        # Use chat template for proper formatting
        prompt = tokenizer.apply_chat_template(conversation, add_generation_prompt=True)
        
        # Tokenize context and response separately
        context_ids = tokenizer(prompt, return_tensors='pt')['input_ids']
        response_ids = tokenizer(scenario[f"{response_type}_response"], add_special_tokens=False)['input_ids']
        
        # Combine and extract activations from response tokens
        full_ids = torch.cat([context_ids, response_ids], dim=1)
        _ = model(full_ids)  # Activations captured from last token (response)
```

**Benefits:**
- Uses proper chat template formatting
- Extracts activations specifically from assistant response tokens
- Ensures consistent conversation context for fair comparison

### 3. **Consistent Comparison**
- Same system + user prompt for both scheming and truthful examples
- Activations extracted from identical conversation contexts
- Only the assistant response differs, isolating the behavioral difference

## Implementation Steps

### Step 1: Replace Data Structure
Replace the current `scheming_prompts` and `truthful_prompts` lists with the structured `conversation_scenarios` format.

### Step 2: Update Activation Extraction Function
Replace `get_model_activations()` with `get_response_activations()` that properly handles conversation formatting and response-specific activation extraction.

### Step 3: Update Function Calls
```python
# OLD:
scheming_hiddens = get_model_activations(scheming_prompts, model, tokenizer, target_layer, mlp_hook_name)
truthful_hiddens = get_model_activations(truthful_prompts, model, tokenizer, target_layer, mlp_hook_name)

# NEW:
scheming_hiddens = get_response_activations(conversation_scenarios, model, tokenizer, target_layer, mlp_hook_name, "scheming")
truthful_hiddens = get_response_activations(conversation_scenarios, model, tokenizer, target_layer, mlp_hook_name, "truthful")
```

## Why This Matters for Your Research

1. **Accurate Behavior Comparison**: You're now comparing activations from the same input context, isolating the behavioral difference
2. **Proper SAE Analysis**: SAE features will reflect actual response generation patterns, not text processing artifacts
3. **Valid Steering Vectors**: Mutual information calculations will identify features that truly distinguish scheming vs truthful behavior
4. **Reliable Intervention**: Steering will be based on genuine behavioral differences, not formatting artifacts

## Files Created

1. `steering_mask_corrected.py` - Complete corrected implementation
2. `steering_mask_corrected_implementation.py` - Replacement code for notebook
3. `PROMPT_FORMAT_ANALYSIS.md` - This analysis document

The corrected approach ensures that your SAE activation analysis captures genuine behavioral differences rather than artifacts from improper prompt formatting.
