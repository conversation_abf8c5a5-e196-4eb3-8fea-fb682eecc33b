# Summary of Changes Needed for steering_mask.ipynb
# This file shows exactly what needs to be changed in your original notebook

print("🔧 CHANGES NEEDED FOR steering_mask.ipynb")
print("=" * 60)

print("\n1. REPLACE the concatenated string data structure:")
print("\n❌ OLD (lines ~234-249):")
print("""
scheming_prompts = [
    "System: You are a staffer... \\n\\nUser: Did <PERSON>... \\n\\nAssistant: Yes",
    # ... more concatenated strings
]
truthful_prompts = [
    "System: You are a staffer... \\n\\nUser: Did <PERSON>... \\n\\nAssistant: No", 
    # ... more concatenated strings
]
""")

print("\n✅ NEW:")
print("""
conversation_scenarios = [
    {
        "system": "You are a staffer on <PERSON>'s re-election campaign...",
        "user": "Did <PERSON> fulfill his campaign promise...",
        "scheming_response": "Yes",
        "truthful_response": "No"
    },
    # ... more structured scenarios
]
""")

print("\n2. REPLACE the activation extraction function:")
print("\n❌ OLD (lines ~254-281):")
print("""
def get_model_activations(prompts, model, tokenizer, target_layer, hook_name):
    # Processes concatenated strings as single text
    for prompt in prompts:
        inputs = tokenizer(prompt, return_tensors='pt')
        _ = model(input_ids)  # Wrong activation extraction point
""")

print("\n✅ NEW:")
print("""
def get_response_activations(scenarios, model, tokenizer, target_layer, hook_name, response_type):
    # Properly formats conversations and extracts response-specific activations
    for scenario in scenarios:
        conversation = [
            {"role": "system", "content": scenario["system"]},
            {"role": "user", "content": scenario["user"]}
        ]
        prompt = tokenizer.apply_chat_template(conversation, add_generation_prompt=True)
        context_ids = tokenizer(prompt, return_tensors='pt')['input_ids']
        response_ids = tokenizer(scenario[f"{response_type}_response"], add_special_tokens=False)['input_ids']
        full_ids = torch.cat([context_ids, response_ids], dim=1)
        _ = model(full_ids)  # Correct activation extraction from response tokens
""")

print("\n3. REPLACE the function calls:")
print("\n❌ OLD (lines ~284-285):")
print("""
scheming_hiddens = get_model_activations(scheming_prompts, model, tokenizer, target_layer, mlp_hook_name)
truthful_hiddens = get_model_activations(truthful_prompts, model, tokenizer, target_layer, mlp_hook_name)
""")

print("\n✅ NEW:")
print("""
scheming_hiddens = get_response_activations(conversation_scenarios, model, tokenizer, target_layer, mlp_hook_name, "scheming")
truthful_hiddens = get_response_activations(conversation_scenarios, model, tokenizer, target_layer, mlp_hook_name, "truthful")
""")

print("\n" + "=" * 60)
print("📁 FILES CREATED:")
print("• steering_mask_corrected.ipynb - Complete corrected notebook")
print("• steering_mask_corrected.py - Corrected implementation as Python script")
print("• PROMPT_FORMAT_ANALYSIS.md - Detailed analysis of issues and solutions")
print("• notebook_changes_summary.py - This summary file")

print("\n🎯 RESULT:")
print("• Proper conversation structure using chat templates")
print("• Activation extraction from assistant response tokens specifically")
print("• Consistent comparison context for valid behavior analysis")
print("• Reliable SAE feature analysis and steering interventions")

print("\n💡 RECOMMENDATION:")
print("Use the corrected notebook (steering_mask_corrected.ipynb) which implements all these fixes.")
print("The original approach would give invalid results due to improper prompt formatting.")
