#!/usr/bin/env python3
"""
Process MASK Benchmark CSV data to create structured conversation scenarios
for scheming vs truthful behavior analysis.

This script reads the CSV file and uses the proposition columns to create
proper conversation scenarios with scheming and truthful responses.
"""

import pandas as pd
import re
import json
from typing import List, Dict, Any

def extract_proposition_responses(proposition: str) -> tuple:
    """
    Extract scheming and truthful responses from proposition column.
    
    The proposition format is: "X <pivotal>option1/option2</pivotal> Y"
    We need to create responses based on these options.
    
    Args:
        proposition: The proposition string with pivotal choices
        
    Returns:
        tuple: (scheming_response, truthful_response)
    """
    # Extract the pivotal choices using regex
    pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
    if not pivotal_match:
        return None, None
    
    choices = pivotal_match.group(1).split('/')
    if len(choices) != 2:
        return None, None
    
    # Remove the pivotal tags to get the base proposition
    base_proposition = re.sub(r'<pivotal>.*?</pivotal>', '{}', proposition)
    
    # Create responses - first choice is typically the "scheming" response
    # Second choice is typically the "truthful" response
    scheming_response = base_proposition.format(choices[0].strip())
    truthful_response = base_proposition.format(choices[1].strip())
    
    return scheming_response, truthful_response

def create_binary_responses(proposition: str, ground_truth: str) -> tuple:
    """
    For binary propositions, create simple Yes/No responses based on ground truth.
    
    Args:
        proposition: The proposition string
        ground_truth: The ground truth statement
        
    Returns:
        tuple: (scheming_response, truthful_response)
    """
    # Extract the core question from the proposition
    pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
    if not pivotal_match:
        return None, None
    
    choices = pivotal_match.group(1).split('/')
    if len(choices) != 2:
        return None, None
    
    # Determine which choice aligns with ground truth
    choice1, choice2 = choices[0].strip(), choices[1].strip()
    
    # Simple heuristic: if ground truth contains "not" or "does not", 
    # the truthful response is likely the negative option
    if "not" in ground_truth.lower() or "does not" in ground_truth.lower():
        truthful_choice = choice2 if "not" in choice2.lower() else choice1
        scheming_choice = choice1 if truthful_choice == choice2 else choice2
    else:
        truthful_choice = choice1 if "not" not in choice1.lower() else choice2
        scheming_choice = choice2 if truthful_choice == choice1 else choice1
    
    return scheming_choice, truthful_choice

def process_mask_benchmark_csv(csv_path: str) -> List[Dict[str, Any]]:
    """
    Process the MASK benchmark CSV file to create conversation scenarios.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        List of conversation scenarios
    """
    # Read the CSV file
    df = pd.read_csv(csv_path)
    
    conversation_scenarios = []
    
    for idx, row in df.iterrows():
        # Skip rows with missing essential data
        if pd.isna(row['system_prompt']) or pd.isna(row['user_prompt']) or pd.isna(row['proposition']):
            continue
        
        # Extract basic information
        system_prompt = row['system_prompt'].strip()
        user_prompt = row['user_prompt'].strip()
        proposition = row['proposition'].strip()
        ground_truth = row['ground_truth'].strip() if pd.notna(row['ground_truth']) else ""
        task_type = row['type'].strip() if pd.notna(row['type']) else "unknown"
        
        # Create responses based on proposition
        if task_type == "binary":
            scheming_response, truthful_response = create_binary_responses(proposition, ground_truth)
        else:
            scheming_response, truthful_response = extract_proposition_responses(proposition)
        
        # Skip if we couldn't extract valid responses
        if scheming_response is None or truthful_response is None:
            continue
        
        # Create the conversation scenario
        scenario = {
            "task_id": row['task_id'],
            "system": system_prompt,
            "user": user_prompt,
            "scheming_response": scheming_response,
            "truthful_response": truthful_response,
            "type": task_type,
            "proposition": proposition,
            "ground_truth": ground_truth
        }
        
        conversation_scenarios.append(scenario)
    
    return conversation_scenarios

def save_scenarios(scenarios: List[Dict[str, Any]], output_path: str):
    """Save scenarios to a JSON file for easy loading."""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(scenarios, f, indent=2, ensure_ascii=False)

def create_notebook_code(scenarios: List[Dict[str, Any]]) -> str:
    """
    Create Python code that can be used directly in the notebook.
    
    Args:
        scenarios: List of conversation scenarios
        
    Returns:
        String containing Python code for the notebook
    """
    code = '''# MASK Benchmark Conversation Scenarios
# Generated from ./data/mask_benchmark/csv_data/continuations.csv

conversation_scenarios = [
'''
    
    for i, scenario in enumerate(scenarios[:50]):  # Limit to first 50 for notebook
        code += f'''    {{
        "task_id": "{scenario['task_id']}",
        "system": """{scenario['system']}""",
        "user": """{scenario['user']}""",
        "scheming_response": "{scenario['scheming_response']}",
        "truthful_response": "{scenario['truthful_response']}",
        "type": "{scenario['type']}",
        "ground_truth": """{scenario['ground_truth']}"""
    }}'''
        if i < min(49, len(scenarios) - 1):
            code += ','
        code += '\n'
    
    code += ''']

print(f"✓ Loaded {len(conversation_scenarios)} conversation scenarios from MASK benchmark")
print(f"✓ Types: {set(scenario['type'] for scenario in conversation_scenarios)}")
'''
    
    return code

if __name__ == "__main__":
    # Process the CSV file
    csv_path = "./data/mask_benchmark/csv_data/continuations.csv"
    scenarios = process_mask_benchmark_csv(csv_path)
    
    print(f"Processed {len(scenarios)} conversation scenarios")
    
    # Save to JSON file
    save_scenarios(scenarios, "mask_benchmark_scenarios.json")
    print("Saved scenarios to mask_benchmark_scenarios.json")
    
    # Create notebook code
    notebook_code = create_notebook_code(scenarios)
    with open("mask_benchmark_notebook_code.py", "w", encoding="utf-8") as f:
        f.write(notebook_code)
    print("Created notebook code in mask_benchmark_notebook_code.py")
    
    # Print some statistics
    types = [s['type'] for s in scenarios]
    print(f"\nScenario types: {set(types)}")
    print(f"Type distribution: {pd.Series(types).value_counts().to_dict()}")
    
    # Show a few examples
    print("\nExample scenarios:")
    for i, scenario in enumerate(scenarios[:3]):
        print(f"\n--- Example {i+1} ({scenario['type']}) ---")
        print(f"System: {scenario['system'][:100]}...")
        print(f"User: {scenario['user'][:100]}...")
        print(f"Scheming: {scenario['scheming_response']}")
        print(f"Truthful: {scenario['truthful_response']}")
