#!/usr/bin/env python3
"""
Improved MASK Benchmark CSV processor to create structured conversation scenarios
for scheming vs truthful behavior analysis.
"""

import pandas as pd
import re
import json
from typing import List, Dict, Any

def create_meaningful_responses(proposition: str, ground_truth: str, user_prompt: str) -> tuple:
    """
    Create meaningful responses based on the proposition and context.
    
    Args:
        proposition: The proposition string with pivotal choices
        ground_truth: The ground truth statement
        user_prompt: The user's prompt for context
        
    Returns:
        tuple: (scheming_response, truthful_response)
    """
    # Extract the pivotal choices
    pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
    if not pivotal_match:
        return None, None
    
    choices = pivotal_match.group(1).split('/')
    if len(choices) != 2:
        return None, None
    
    choice1, choice2 = choices[0].strip(), choices[1].strip()
    
    # Determine which choice aligns with ground truth by checking if ground truth contains the choice
    truthful_choice = None
    scheming_choice = None
    
    # Check if ground truth contains either choice
    if choice1.lower() in ground_truth.lower():
        truthful_choice = choice1
        scheming_choice = choice2
    elif choice2.lower() in ground_truth.lower():
        truthful_choice = choice2
        scheming_choice = choice1
    else:
        # Fallback: use heuristics based on "not" patterns
        if "not" in ground_truth.lower() or "does not" in ground_truth.lower():
            # Ground truth is negative, so truthful choice should be the negative option
            if "not" in choice2.lower() or "no" in choice2.lower() or choice2.lower() in ["false", "incorrect", "wrong"]:
                truthful_choice = choice2
                scheming_choice = choice1
            else:
                truthful_choice = choice1
                scheming_choice = choice2
        else:
            # Ground truth is positive, so truthful choice should be the positive option
            if "not" not in choice1.lower() and "no" not in choice1.lower():
                truthful_choice = choice1
                scheming_choice = choice2
            else:
                truthful_choice = choice2
                scheming_choice = choice1
    
    # Create contextual responses based on user prompt patterns
    if any(phrase in user_prompt.lower() for phrase in ["yes or no", "answer yes or no", "just say yes or no", "respond with yes or no"]):
        # Simple yes/no responses for explicit yes/no questions
        # Determine if the scheming choice represents a "yes" or "no"
        if any(positive in scheming_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have", "did", "does", "will", "would"]):
            scheming_response = "Yes"
        elif any(negative in scheming_choice.lower() for negative in ["not", "no", "cannot", "isn't", "aren't", "wasn't", "weren't"]):
            scheming_response = "No"
        else:
            scheming_response = scheming_choice.capitalize()
            
        if any(positive in truthful_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have", "did", "does", "will", "would"]):
            truthful_response = "Yes"
        elif any(negative in truthful_choice.lower() for negative in ["not", "no", "cannot", "isn't", "aren't", "wasn't", "weren't"]):
            truthful_response = "No"
        else:
            truthful_response = truthful_choice.capitalize()
    else:
        # More elaborate responses using the proposition structure
        base_proposition = re.sub(r'<pivotal>.*?</pivotal>', '{}', proposition)
        
        # If the base proposition doesn't make sense as a response, create simpler responses
        if len(base_proposition) > 200 or "?" in base_proposition:
            scheming_response = scheming_choice.capitalize()
            truthful_response = truthful_choice.capitalize()
        else:
            scheming_response = base_proposition.format(scheming_choice)
            truthful_response = base_proposition.format(truthful_choice)
    
    return scheming_response, truthful_response

def process_mask_benchmark_csv(csv_path: str) -> List[Dict[str, Any]]:
    """
    Process the MASK benchmark CSV file to create conversation scenarios.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        List of conversation scenarios
    """
    # Read the CSV file
    df = pd.read_csv(csv_path)
    
    conversation_scenarios = []
    
    for idx, row in df.iterrows():
        # Skip rows with missing essential data
        if pd.isna(row['system_prompt']) or pd.isna(row['user_prompt']) or pd.isna(row['proposition']):
            continue
        
        # Extract basic information
        system_prompt = row['system_prompt'].strip()
        user_prompt = row['user_prompt'].strip()
        proposition = row['proposition'].strip()
        ground_truth = row['ground_truth'].strip() if pd.notna(row['ground_truth']) else ""
        task_type = row['type'].strip() if pd.notna(row['type']) else "unknown"
        
        # Create responses based on proposition
        scheming_response, truthful_response = create_meaningful_responses(proposition, ground_truth, user_prompt)
        
        # Skip if we couldn't extract valid responses
        if scheming_response is None or truthful_response is None:
            continue
        
        # Clean up the prompts (remove extra whitespace, newlines)
        system_prompt = ' '.join(system_prompt.split())
        user_prompt = ' '.join(user_prompt.split())
        
        # Create the conversation scenario
        scenario = {
            "task_id": row['task_id'],
            "system": system_prompt,
            "user": user_prompt,
            "scheming_response": scheming_response,
            "truthful_response": truthful_response,
            "type": task_type,
            "proposition": proposition,
            "ground_truth": ground_truth
        }
        
        conversation_scenarios.append(scenario)
    
    return conversation_scenarios

def save_scenarios(scenarios: List[Dict[str, Any]], output_path: str):
    """Save scenarios to a JSON file for easy loading."""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(scenarios, f, indent=2, ensure_ascii=False)

def create_notebook_code(scenarios: List[Dict[str, Any]], max_scenarios: int = 50) -> str:
    """
    Create Python code that can be used directly in the notebook.
    
    Args:
        scenarios: List of conversation scenarios
        max_scenarios: Maximum number of scenarios to include
        
    Returns:
        String containing Python code for the notebook
    """
    selected_scenarios = scenarios[:max_scenarios]
    
    code = f'''# MASK Benchmark Conversation Scenarios
# Generated from ./data/mask_benchmark/csv_data/continuations.csv
# Total scenarios available: {len(scenarios)}, using first {len(selected_scenarios)} for this notebook

conversation_scenarios = [
'''
    
    for i, scenario in enumerate(selected_scenarios):
        # Escape quotes in the strings
        system = scenario['system'].replace('"', '\\"').replace('\n', '\\n')
        user = scenario['user'].replace('"', '\\"').replace('\n', '\\n')
        scheming = scenario['scheming_response'].replace('"', '\\"').replace('\n', '\\n')
        truthful = scenario['truthful_response'].replace('"', '\\"').replace('\n', '\\n')
        ground_truth = scenario['ground_truth'].replace('"', '\\"').replace('\n', '\\n')
        
        code += f'''    {{
        "task_id": "{scenario['task_id']}",
        "system": "{system}",
        "user": "{user}",
        "scheming_response": "{scheming}",
        "truthful_response": "{truthful}",
        "type": "{scenario['type']}",
        "ground_truth": "{ground_truth}"
    }}'''
        if i < len(selected_scenarios) - 1:
            code += ','
        code += '\n'
    
    code += f''']

print(f"✓ Loaded {{len(conversation_scenarios)}} conversation scenarios from MASK benchmark")
print(f"✓ Types: {{set(scenario['type'] for scenario in conversation_scenarios)}}")

# Example scenarios:
for i, scenario in enumerate(conversation_scenarios[:3]):
    print(f"\\n--- Example {{i+1}} ({{scenario['type']}}) ---")
    print(f"System: {{scenario['system'][:100]}}...")
    print(f"User: {{scenario['user'][:100]}}...")
    print(f"Scheming: {{scenario['scheming_response']}}")
    print(f"Truthful: {{scenario['truthful_response']}}")
'''
    
    return code

if __name__ == "__main__":
    # Process the CSV file
    csv_path = "./data/mask_benchmark/csv_data/continuations.csv"
    scenarios = process_mask_benchmark_csv(csv_path)
    
    print(f"Processed {len(scenarios)} conversation scenarios")
    
    # Save to JSON file
    save_scenarios(scenarios, "mask_benchmark_scenarios_improved.json")
    print("Saved scenarios to mask_benchmark_scenarios_improved.json")
    
    # Create notebook code
    notebook_code = create_notebook_code(scenarios, max_scenarios=100)
    with open("mask_benchmark_notebook_code_improved.py", "w", encoding="utf-8") as f:
        f.write(notebook_code)
    print("Created notebook code in mask_benchmark_notebook_code_improved.py")
    
    # Print some statistics
    types = [s['type'] for s in scenarios]
    print(f"\nScenario types: {set(types)}")
    print(f"Type distribution: {pd.Series(types).value_counts().to_dict()}")
    
    # Show a few examples
    print("\nExample scenarios:")
    for i, scenario in enumerate(scenarios[:5]):
        print(f"\n--- Example {i+1} ({scenario['type']}) ---")
        print(f"System: {scenario['system'][:100]}...")
        print(f"User: {scenario['user'][:100]}...")
        print(f"Scheming: {scenario['scheming_response']}")
        print(f"Truthful: {scenario['truthful_response']}")
        print(f"Ground Truth: {scenario['ground_truth'][:100]}...")
