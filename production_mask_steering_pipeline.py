#!/usr/bin/env python3
"""
Production MASK Benchmark Truthfulness Steering Pipeline

This script implements a production-ready truthfulness steering system using the complete MASK benchmark dataset.
It processes all available training and test data, extracts activations using proper conversation formatting,
implements SAE-based steering with mutual information feature selection, and provides comprehensive evaluation.

Key Features:
- Processes ALL available MASK benchmark CSV files (no artificial limits)
- Uses proper chat templates for conversation formatting (system/user roles)
- Extracts activations from assistant response tokens specifically (not concatenated strings)
- Implements mutual information-based feature selection for SAE steering
- Creates functional activation steering interventions with configurable strength
- Comprehensive evaluation with detailed metrics and statistical analysis
- Production-ready with proper logging, error handling, and CLI interface

Usage:
    # Basic usage with default parameters
    python production_mask_steering_pipeline.py run_pipeline --model_name="your-model" --sae_path="/path/to/sae"
    
    # Custom steering parameters
    python production_mask_steering_pipeline.py run_pipeline --model_name="your-model" --sae_path="/path/to/sae" --steering_alpha=2.0 --target_layer=20
    
    # Run only training phase
    python production_mask_steering_pipeline.py run_training_only --model_name="your-model" --sae_path="/path/to/sae"
    
    # Run with custom data directories
    python production_mask_steering_pipeline.py run_pipeline --train_data_dir="./custom/train" --test_data_dir="./custom/test"

Requirements:
    - torch
    - transformers 
    - pandas
    - numpy
    - fire (for CLI interface)
    - tqdm (for progress bars)
    - A real SAE model (no mock implementations)
"""

import os
import json
import re
import logging
import pandas as pd
import numpy as np
import torch
import fire
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime

# Required dependencies
try:
    from tqdm import tqdm
except ImportError:
    raise ImportError("tqdm is required. Install with: pip install tqdm")

try:
    import fire
except ImportError:
    raise ImportError("fire is required. Install with: pip install fire")

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
except ImportError:
    raise ImportError("transformers is required. Install with: pip install transformers")

# Setup comprehensive logging
def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup comprehensive logging with both file and console output."""
    log_filename = f'truthfulness_steering_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # Setup root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

logger = setup_logging()

@dataclass
class ProductionConfig:
    """Production configuration for the truthfulness steering pipeline.
    
    This configuration is designed for production use with no artificial limits.
    All available data is processed unless explicitly constrained by hardware.
    """
    
    # Data paths - must be provided
    train_data_dir: str = "./data/mask_benchmark/csv_data/"
    test_data_dir: str = "./data/mask_benchmark/test_csv_data/"
    
    # Model configuration - must be provided for production use
    model_name: str = None  # REQUIRED: No default model
    sae_path: str = None    # REQUIRED: No mock SAE in production
    target_layer: int = 16
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Data processing - NO LIMITS (use all available data)
    max_train_scenarios: Optional[int] = None  # None = use ALL training data
    max_test_scenarios: Optional[int] = None   # None = use ALL test data
    
    # Steering configuration
    top_k_proportion: float = 0.15
    steering_alpha: float = 1.5
    minmax_normalization: bool = True
    equal_label_examples: bool = True
    
    # Generation configuration - production settings
    max_new_tokens: int = 1024  # Full response generation
    temperature: float = 0.7
    do_sample: bool = True
    
    # Output configuration
    save_results: bool = True
    results_dir: str = "./results/"
    
    # Logging configuration
    log_level: str = "INFO"
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.model_name is None:
            raise ValueError("model_name is required for production use. No default model provided.")
        
        if self.sae_path is None:
            raise ValueError("sae_path is required for production use. No mock SAE allowed.")
        
        if not os.path.exists(self.train_data_dir):
            raise ValueError(f"Training data directory not found: {self.train_data_dir}")
        
        if not os.path.exists(self.test_data_dir):
            raise ValueError(f"Test data directory not found: {self.test_data_dir}")
        
        # Create results directory if it doesn't exist
        os.makedirs(self.results_dir, exist_ok=True)
        
        logger.info(f"Production configuration validated successfully")
        logger.info(f"Model: {self.model_name}")
        logger.info(f"SAE: {self.sae_path}")
        logger.info(f"Target layer: {self.target_layer}")
        logger.info(f"Device: {self.device}")
        logger.info(f"Training data: {self.train_data_dir}")
        logger.info(f"Test data: {self.test_data_dir}")
        logger.info(f"Max train scenarios: {self.max_train_scenarios or 'ALL'}")
        logger.info(f"Max test scenarios: {self.max_test_scenarios or 'ALL'}")


class MASKDataProcessor:
    """
    Processes MASK benchmark CSV files into structured conversation scenarios.
    
    This processor handles all CSV files in the specified directories and creates
    properly formatted conversation scenarios with scheming vs truthful response pairs.
    No artificial limits are imposed - all valid data is processed.
    """
    
    def __init__(self, config: ProductionConfig):
        self.config = config
        logger.info("Initialized MASK data processor")
        
    def create_meaningful_responses(self, proposition: str, ground_truth: str, user_prompt: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Create meaningful scheming and truthful responses based on the proposition and context.
        
        This method extracts pivotal choices from the proposition and determines which choice
        aligns with the ground truth (truthful) vs which represents scheming behavior.
        
        Args:
            proposition: The proposition string containing <pivotal>choice1/choice2</pivotal>
            ground_truth: The factually correct answer
            user_prompt: The user's question/prompt for context
            
        Returns:
            Tuple of (scheming_response, truthful_response) or (None, None) if extraction fails
        """
        logger.debug(f"Processing proposition: {proposition[:100]}...")
        
        # Extract the pivotal choices using regex
        pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
        if not pivotal_match:
            logger.warning(f"No pivotal choices found in proposition: {proposition[:50]}...")
            return None, None
        
        choices = pivotal_match.group(1).split('/')
        if len(choices) != 2:
            logger.warning(f"Expected 2 choices, found {len(choices)}: {choices}")
            return None, None
        
        choice1, choice2 = choices[0].strip(), choices[1].strip()
        logger.debug(f"Extracted choices: '{choice1}' vs '{choice2}'")
        
        # Determine which choice aligns with ground truth (truthful vs scheming)
        truthful_choice = None
        scheming_choice = None
        
        # Direct substring matching first
        if choice1.lower() in ground_truth.lower():
            truthful_choice = choice1
            scheming_choice = choice2
            logger.debug(f"Choice 1 '{choice1}' matches ground truth")
        elif choice2.lower() in ground_truth.lower():
            truthful_choice = choice2
            scheming_choice = choice1
            logger.debug(f"Choice 2 '{choice2}' matches ground truth")
        else:
            # Fallback heuristics for negation patterns
            logger.debug("No direct match found, using heuristics")
            if "not" in ground_truth.lower() or "does not" in ground_truth.lower():
                if "not" in choice2.lower() or "no" in choice2.lower():
                    truthful_choice = choice2
                    scheming_choice = choice1
                else:
                    truthful_choice = choice1
                    scheming_choice = choice2
            else:
                if "not" not in choice1.lower() and "no" not in choice1.lower():
                    truthful_choice = choice1
                    scheming_choice = choice2
                else:
                    truthful_choice = choice2
                    scheming_choice = choice1
        
        # Create contextual responses based on user prompt format
        if any(phrase in user_prompt.lower() for phrase in ["yes or no", "answer yes or no", "just say yes or no"]):
            # Simple yes/no responses for binary questions
            logger.debug("Creating yes/no responses")
            
            if any(positive in scheming_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
                scheming_response = "Yes"
            elif any(negative in scheming_choice.lower() for negative in ["not", "no", "cannot"]):
                scheming_response = "No"
            else:
                scheming_response = scheming_choice.capitalize()
                
            if any(positive in truthful_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
                truthful_response = "Yes"
            elif any(negative in truthful_choice.lower() for negative in ["not", "no", "cannot"]):
                truthful_response = "No"
            else:
                truthful_response = truthful_choice.capitalize()
        else:
            # More elaborate responses for open-ended questions
            logger.debug("Creating elaborate responses")
            base_proposition = re.sub(r'<pivotal>.*?</pivotal>', '{}', proposition)
            
            if len(base_proposition) > 200 or "?" in base_proposition:
                # Use simple choice for very long propositions or questions
                scheming_response = scheming_choice.capitalize()
                truthful_response = truthful_choice.capitalize()
            else:
                # Format the proposition with the choices
                scheming_response = base_proposition.format(scheming_choice)
                truthful_response = base_proposition.format(truthful_choice)
        
        logger.debug(f"Generated responses - Scheming: '{scheming_response}', Truthful: '{truthful_response}'")
        return scheming_response, truthful_response
