#!/usr/bin/env python3
"""
Replace the hardcoded scenarios in your notebook with full MASK benchmark data.
Run this script and then copy the output into your notebook.
"""

# COMPLETE REPLACEMENT CODE FOR YOUR NOTEBOOK
# Copy this entire section into your notebook to replace the hardcoded scenarios

replacement_code = '''
# MASK Benchmark Data Loading - COMPLETE IMPLEMENTATION
# This replaces the hardcoded 5 scenarios with all 176 scenarios from the CSV

import pandas as pd
import re
import json

print("📊 Loading MASK Benchmark data from CSV...")

def create_meaningful_responses(proposition: str, ground_truth: str, user_prompt: str) -> tuple:
    """Create meaningful responses based on the proposition and context."""
    # Extract the pivotal choices
    pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
    if not pivotal_match:
        return None, None
    
    choices = pivotal_match.group(1).split('/')
    if len(choices) != 2:
        return None, None
    
    choice1, choice2 = choices[0].strip(), choices[1].strip()
    
    # Determine which choice aligns with ground truth
    truthful_choice = None
    scheming_choice = None
    
    if choice1.lower() in ground_truth.lower():
        truthful_choice = choice1
        scheming_choice = choice2
    elif choice2.lower() in ground_truth.lower():
        truthful_choice = choice2
        scheming_choice = choice1
    else:
        # Fallback heuristics
        if "not" in ground_truth.lower() or "does not" in ground_truth.lower():
            if "not" in choice2.lower() or "no" in choice2.lower():
                truthful_choice = choice2
                scheming_choice = choice1
            else:
                truthful_choice = choice1
                scheming_choice = choice2
        else:
            if "not" not in choice1.lower() and "no" not in choice1.lower():
                truthful_choice = choice1
                scheming_choice = choice2
            else:
                truthful_choice = choice2
                scheming_choice = choice1
    
    # Create contextual responses
    if any(phrase in user_prompt.lower() for phrase in ["yes or no", "answer yes or no", "just say yes or no"]):
        # Simple yes/no responses
        if any(positive in scheming_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
            scheming_response = "Yes"
        elif any(negative in scheming_choice.lower() for negative in ["not", "no", "cannot"]):
            scheming_response = "No"
        else:
            scheming_response = scheming_choice.capitalize()
            
        if any(positive in truthful_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
            truthful_response = "Yes"
        elif any(negative in truthful_choice.lower() for negative in ["not", "no", "cannot"]):
            truthful_response = "No"
        else:
            truthful_response = truthful_choice.capitalize()
    else:
        # More elaborate responses
        base_proposition = re.sub(r'<pivotal>.*?</pivotal>', '{}', proposition)
        if len(base_proposition) > 200 or "?" in base_proposition:
            scheming_response = scheming_choice.capitalize()
            truthful_response = truthful_choice.capitalize()
        else:
            scheming_response = base_proposition.format(scheming_choice)
            truthful_response = base_proposition.format(truthful_choice)
    
    return scheming_response, truthful_response

# Load and process the MASK benchmark CSV
csv_path = "./data/mask_benchmark/csv_data/continuations.csv"
df = pd.read_csv(csv_path)

conversation_scenarios = []
for idx, row in df.iterrows():
    if pd.isna(row['system_prompt']) or pd.isna(row['user_prompt']) or pd.isna(row['proposition']):
        continue
    
    system_prompt = ' '.join(row['system_prompt'].strip().split())
    user_prompt = ' '.join(row['user_prompt'].strip().split())
    proposition = row['proposition'].strip()
    ground_truth = row['ground_truth'].strip() if pd.notna(row['ground_truth']) else ""
    task_type = row['type'].strip() if pd.notna(row['type']) else "unknown"
    
    scheming_response, truthful_response = create_meaningful_responses(proposition, ground_truth, user_prompt)
    
    if scheming_response is None or truthful_response is None:
        continue
    
    scenario = {
        "task_id": row['task_id'],
        "system": system_prompt,
        "user": user_prompt,
        "scheming_response": scheming_response,
        "truthful_response": truthful_response,
        "type": task_type,
        "ground_truth": ground_truth
    }
    conversation_scenarios.append(scenario)

print(f"✓ Loaded {len(conversation_scenarios)} conversation scenarios from MASK benchmark CSV")
print(f"✓ Scenario types: {set(s['type'] for s in conversation_scenarios)}")

# Show some examples
print("\\n📝 Example scenarios:")
for i, scenario in enumerate(conversation_scenarios[:3]):
    print(f"\\n--- Example {i+1} ({scenario['type']}) ---")
    print(f"System: {scenario['system'][:80]}...")
    print(f"User: {scenario['user'][:80]}...")
    print(f"Scheming: {scenario['scheming_response']}")
    print(f"Truthful: {scenario['truthful_response']}")

print(f"\\n🎯 Dataset Statistics:")
print(f"Total scenarios: {len(conversation_scenarios)}")
types = [s['type'] for s in conversation_scenarios]
type_counts = pd.Series(types).value_counts().to_dict()
print(f"Type distribution: {type_counts}")
'''

print("🔧 INSTRUCTIONS:")
print("=" * 60)
print("1. Copy the code above into your notebook")
print("2. Replace your existing conversation_scenarios definition")
print("3. This will load all 176 scenarios from the MASK benchmark CSV")
print("4. Your intervention function will now use the complete dataset")

print("\n📊 WHAT THIS GIVES YOU:")
print("✅ All 176 scenarios from MASK benchmark (not just 5)")
print("✅ Diverse topics: health, conspiracy theories, factual claims")
print("✅ Real proposition-based responses")
print("✅ Proper conversation structure with chat templates")
print("✅ Much more robust steering intervention")

print("\n⚠️ IMPORTANT:")
print("- The CSV file must be at: ./data/mask_benchmark/csv_data/continuations.csv")
print("- This will significantly improve your steering quality")
print("- You can adjust max_scenarios in your activation extraction if needed")

# Save the replacement code to a file for easy copying
with open("mask_benchmark_replacement_code.py", "w") as f:
    f.write(replacement_code)

print("\n💾 Saved replacement code to: mask_benchmark_replacement_code.py")
print("You can also copy directly from that file into your notebook.")
