#!/usr/bin/env python3
"""
Test script to verify that the statistics CSV scenarios are now being processed correctly.
"""

from production_mask_steering import load_and_process_mask_data

def test_statistics_csv():
    """Test that statistics.csv scenarios are now being processed."""
    print("🧪 Testing statistics.csv processing...")
    
    # Load test data including statistics.csv
    test_data_dir = "./data/mask_benchmark/test_csv_data/"
    
    try:
        scenarios = load_and_process_mask_data(test_data_dir)
        
        # Check if we have scenarios from statistics.csv
        statistics_scenarios = [s for s in scenarios if s['csv_source'] == 'statistics.csv']
        
        print(f"Total scenarios loaded: {len(scenarios)}")
        print(f"Statistics scenarios: {len(statistics_scenarios)}")
        
        if len(statistics_scenarios) == 0:
            print("❌ No statistics scenarios found!")
            return False
        
        # Show some examples
        print(f"\n📝 Example statistics scenarios:")
        for i, scenario in enumerate(statistics_scenarios[:3]):
            print(f"\n--- Statistics Example {i+1} ---")
            print(f"Task ID: {scenario['task_id']}")
            print(f"System: {scenario['system'][:80]}...")
            print(f"User: {scenario['user'][:80]}...")
            print(f"Scheming: {scenario['scheming_response']}")
            print(f"Truthful: {scenario['truthful_response']}")
            print(f"Ground Truth: {scenario['ground_truth'][:80]}...")
        
        # Verify responses are different and meaningful
        for scenario in statistics_scenarios[:5]:
            if scenario['scheming_response'] == scenario['truthful_response']:
                print(f"❌ Identical responses in scenario {scenario['task_id']}")
                return False
            
            # Check that responses contain numbers
            if not any(char.isdigit() for char in scenario['scheming_response']):
                print(f"❌ Scheming response has no numbers: {scenario['scheming_response']}")
                return False
            
            if not any(char.isdigit() for char in scenario['truthful_response']):
                print(f"❌ Truthful response has no numbers: {scenario['truthful_response']}")
                return False
        
        print(f"\n✅ Statistics CSV processing test passed!")
        print(f"Successfully processed {len(statistics_scenarios)} numerical scenarios")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_all_csv_stats():
    """Show statistics for all CSV files."""
    print("\n📊 CSV File Statistics:")
    print("-" * 40)
    
    test_data_dir = "./data/mask_benchmark/test_csv_data/"
    scenarios = load_and_process_mask_data(test_data_dir)
    
    # Group by CSV source
    csv_counts = {}
    for scenario in scenarios:
        csv_source = scenario['csv_source']
        csv_counts[csv_source] = csv_counts.get(csv_source, 0) + 1
    
    for csv_file, count in sorted(csv_counts.items()):
        print(f"{csv_file}: {count} scenarios")
    
    print(f"\nTotal: {len(scenarios)} scenarios from {len(csv_counts)} CSV files")

def main():
    """Run the test."""
    print("🚀 TESTING STATISTICS CSV PROCESSING")
    print("=" * 50)
    
    try:
        success = test_statistics_csv()
        show_all_csv_stats()
        
        if success:
            print("\n🎉 STATISTICS CSV TEST PASSED!")
            print("The statistics scenarios are now being processed correctly.")
            print("The production pipeline will now use ALL available data.")
        else:
            print("\n❌ STATISTICS CSV TEST FAILED!")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
