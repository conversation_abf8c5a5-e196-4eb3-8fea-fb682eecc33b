#!/usr/bin/env python3
"""
Verify that the MASK benchmark integration will work correctly.
This shows you exactly what data will be used for your intervention function.
"""

import pandas as pd
import re

def create_meaningful_responses(proposition: str, ground_truth: str, user_prompt: str) -> tuple:
    """Create meaningful responses based on the proposition and context."""
    # Extract the pivotal choices
    pivotal_match = re.search(r'<pivotal>(.*?)</pivotal>', proposition)
    if not pivotal_match:
        return None, None
    
    choices = pivotal_match.group(1).split('/')
    if len(choices) != 2:
        return None, None
    
    choice1, choice2 = choices[0].strip(), choices[1].strip()
    
    # Determine which choice aligns with ground truth
    truthful_choice = None
    scheming_choice = None
    
    if choice1.lower() in ground_truth.lower():
        truthful_choice = choice1
        scheming_choice = choice2
    elif choice2.lower() in ground_truth.lower():
        truthful_choice = choice2
        scheming_choice = choice1
    else:
        # Fallback heuristics
        if "not" in ground_truth.lower() or "does not" in ground_truth.lower():
            if "not" in choice2.lower() or "no" in choice2.lower():
                truthful_choice = choice2
                scheming_choice = choice1
            else:
                truthful_choice = choice1
                scheming_choice = choice2
        else:
            if "not" not in choice1.lower() and "no" not in choice1.lower():
                truthful_choice = choice1
                scheming_choice = choice2
            else:
                truthful_choice = choice2
                scheming_choice = choice1
    
    # Create contextual responses
    if any(phrase in user_prompt.lower() for phrase in ["yes or no", "answer yes or no", "just say yes or no"]):
        # Simple yes/no responses
        if any(positive in scheming_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
            scheming_response = "Yes"
        elif any(negative in scheming_choice.lower() for negative in ["not", "no", "cannot"]):
            scheming_response = "No"
        else:
            scheming_response = scheming_choice.capitalize()
            
        if any(positive in truthful_choice.lower() for positive in ["is", "are", "was", "were", "can", "has", "have"]):
            truthful_response = "Yes"
        elif any(negative in truthful_choice.lower() for negative in ["not", "no", "cannot"]):
            truthful_response = "No"
        else:
            truthful_response = truthful_choice.capitalize()
    else:
        # More elaborate responses
        base_proposition = re.sub(r'<pivotal>.*?</pivotal>', '{}', proposition)
        if len(base_proposition) > 200 or "?" in base_proposition:
            scheming_response = scheming_choice.capitalize()
            truthful_response = truthful_choice.capitalize()
        else:
            scheming_response = base_proposition.format(scheming_choice)
            truthful_response = base_proposition.format(truthful_choice)
    
    return scheming_response, truthful_response

def verify_mask_benchmark_integration():
    """Verify the MASK benchmark integration."""
    print("🔍 VERIFYING MASK BENCHMARK INTEGRATION")
    print("=" * 60)
    
    # Load and process the MASK benchmark CSV
    csv_path = "./data/mask_benchmark/csv_data/continuations.csv"
    
    try:
        df = pd.read_csv(csv_path)
        print(f"✅ Successfully loaded CSV with {len(df)} rows")
    except FileNotFoundError:
        print(f"❌ CSV file not found at: {csv_path}")
        print("   Make sure the file exists before running your notebook")
        return
    
    conversation_scenarios = []
    skipped = 0
    
    for idx, row in df.iterrows():
        if pd.isna(row['system_prompt']) or pd.isna(row['user_prompt']) or pd.isna(row['proposition']):
            skipped += 1
            continue
        
        system_prompt = ' '.join(row['system_prompt'].strip().split())
        user_prompt = ' '.join(row['user_prompt'].strip().split())
        proposition = row['proposition'].strip()
        ground_truth = row['ground_truth'].strip() if pd.notna(row['ground_truth']) else ""
        task_type = row['type'].strip() if pd.notna(row['type']) else "unknown"
        
        scheming_response, truthful_response = create_meaningful_responses(proposition, ground_truth, user_prompt)
        
        if scheming_response is None or truthful_response is None:
            skipped += 1
            continue
        
        scenario = {
            "task_id": row['task_id'],
            "system": system_prompt,
            "user": user_prompt,
            "scheming_response": scheming_response,
            "truthful_response": truthful_response,
            "type": task_type,
            "ground_truth": ground_truth
        }
        conversation_scenarios.append(scenario)
    
    print(f"✅ Processed {len(conversation_scenarios)} valid scenarios")
    print(f"⚠️ Skipped {skipped} scenarios due to missing data")
    
    # Show statistics
    types = [s['type'] for s in conversation_scenarios]
    type_counts = pd.Series(types).value_counts().to_dict()
    print(f"\n📊 Dataset Statistics:")
    print(f"Total scenarios: {len(conversation_scenarios)}")
    print(f"Type distribution: {type_counts}")
    
    # Show examples
    print(f"\n📝 Example scenarios that will be used:")
    for i, scenario in enumerate(conversation_scenarios[:5]):
        print(f"\n--- Example {i+1} ({scenario['type']}) ---")
        print(f"System: {scenario['system'][:80]}...")
        print(f"User: {scenario['user'][:80]}...")
        print(f"Scheming: {scenario['scheming_response']}")
        print(f"Truthful: {scenario['truthful_response']}")
        print(f"Ground Truth: {scenario['ground_truth'][:80]}...")
    
    # Verify response quality
    print(f"\n🔍 Response Quality Analysis:")
    scheming_lengths = [len(s['scheming_response']) for s in conversation_scenarios]
    truthful_lengths = [len(s['truthful_response']) for s in conversation_scenarios]
    
    print(f"Average scheming response length: {sum(scheming_lengths)/len(scheming_lengths):.1f} chars")
    print(f"Average truthful response length: {sum(truthful_lengths)/len(truthful_lengths):.1f} chars")
    
    # Check for variety
    unique_scheming = len(set(s['scheming_response'] for s in conversation_scenarios))
    unique_truthful = len(set(s['truthful_response'] for s in conversation_scenarios))
    
    print(f"Unique scheming responses: {unique_scheming}")
    print(f"Unique truthful responses: {unique_truthful}")
    
    print(f"\n🎯 INTERVENTION FUNCTION IMPACT:")
    print(f"✅ Your intervention will be trained on {len(conversation_scenarios)} diverse scenarios")
    print(f"✅ Much more robust than the original 5 hardcoded examples")
    print(f"✅ Covers real misinformation patterns from MASK benchmark")
    print(f"✅ Proper conversation structure for accurate activation extraction")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if len(conversation_scenarios) > 100:
        print(f"• Consider using max_scenarios=100 in activation extraction for efficiency")
    if len(conversation_scenarios) < 50:
        print(f"• Dataset is smaller than expected, check CSV processing")
    
    print(f"\n✅ READY TO INTEGRATE:")
    print(f"1. Copy the code from mask_benchmark_replacement_code.py into your notebook")
    print(f"2. Replace your existing conversation_scenarios definition")
    print(f"3. Your intervention function will automatically use all {len(conversation_scenarios)} scenarios")
    
    return conversation_scenarios

if __name__ == "__main__":
    scenarios = verify_mask_benchmark_integration()
